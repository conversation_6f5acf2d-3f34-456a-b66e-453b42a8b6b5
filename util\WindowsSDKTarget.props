<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

<!-- visual studio doesn't have a way to say "I don't care about the SDK version". Targetting an old version will explicitly fail on new SDKs for some stupid reason. So instead we try to target the latest windows 10 SDK or 8.1 if no windows 10 SDK is installed -->

<!-- Note, in VS2019 this is semi-fixed and you can specify 10.0 to mean 'any windows 10 SDK'. However a) presumably this will break when windows 11 ships. b) it only works if you're using the v142 toolset for no good reason. -->
<PropertyGroup>

	<!-- Fixed Windows SDK version to 10.0.22000.0 -->
	<RenderDocLatestWin10SDKVersion>10.0.22000.0</RenderDocLatestWin10SDKVersion>

	<!-- Use the fixed SDK version -->
	<WindowsTargetPlatformVersion>$(RenderDocLatestWin10SDKVersion)</WindowsTargetPlatformVersion>
</PropertyGroup>

</Project>
