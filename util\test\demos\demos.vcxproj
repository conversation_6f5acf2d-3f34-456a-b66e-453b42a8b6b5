﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Development|Win32">
      <Configuration>Development</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Development|x64">
      <Configuration>Development</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <UseNativeEnvironment>true</UseNativeEnvironment>
    <ProjectGuid>{EC8C6087-E99E-44B3-B8DC-3C9FE7907F47}</ProjectGuid>
    <RootNamespace>demos</RootNamespace>
    <ProjectName>demos</ProjectName>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'" Label="Configuration">
    <LinkIncremental>false</LinkIncremental>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup>
    <OutDir>$(SolutionDir)</OutDir>
    <IntDir>$(ProjectDir)obj\$(Platform)\$(Configuration)\</IntDir>
    <ExecutablePath>$(ExecutablePath)</ExecutablePath>
    <IncludePath>$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
    <ExcludePath>$(ExcludePath)</ExcludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Platform)'=='x64'">
    <TargetName>$(ProjectName)_x64</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Platform)'=='Win32'">
    <TargetName>$(ProjectName)_x86</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Platform)'=='x64'">
    <ClCompile>
      <PreprocessorDefinitions>WIN64;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <PreprocessorDefinitions>RELEASE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <MinimalRebuild>false</MinimalRebuild>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir)vk\official;$(ProjectDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>VK_USE_PLATFORM_WIN32_KHR;WIN32;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DisableSpecificWarnings>4100;4054;4055</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <AdditionalDependencies>kernel32.lib;user32.lib;msimg32.lib;gdi32.lib;shell32.lib;shlwapi.lib</AdditionalDependencies>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Development'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
    </ClCompile>
    <Link>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <PropertyGroup Condition="$(Platform)=='x64' and exists('3rdparty/shaderc/x64/lib/shaderc_combined.lib') and $(PlatformToolset)=='v143'">
    <HaveShaderc>1</HaveShaderc>
  </PropertyGroup>
  <PropertyGroup Condition="$(Platform)=='x86' and exists('3rdparty/shaderc/x86/lib/shaderc_combined.lib') and $(PlatformToolset)=='v143'">
    <HaveShaderc>1</HaveShaderc>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="$(Platform)=='x64' and $(HaveShaderc)=='1'">
    <ClCompile>
      <AdditionalIncludeDirectories>3rdparty/shaderc/x64/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>HAVE_SHADERC=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>3rdparty/shaderc/x64/lib/shaderc_combined.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="$(Platform)=='x86' and $(HaveShaderc)=='1'">
    <ClCompile>
      <AdditionalIncludeDirectories>3rdparty/shaderc/x86/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>HAVE_SHADERC=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>3rdparty/shaderc/x86/lib/shaderc_combined.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="3rdparty\fmt\format.cc" />
    <ClCompile Include="3rdparty\md5\md5.c" />
    <ClCompile Include="android\android_platform.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="android\android_window.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_1_many_uavs.cpp" />
    <ClCompile Include="d3d11\d3d11_amd_shader_extensions.cpp" />
    <ClCompile Include="d3d11\d3d11_array_interpolator.cpp" />
    <ClCompile Include="d3d11\d3d11_binding_hazards.cpp" />
    <ClCompile Include="d3d11\d3d11_buffer_truncation.cpp" />
    <ClCompile Include="d3d11\d3d11_byte_address_buffers.cpp" />
    <ClCompile Include="d3d11\d3d11_cbuffer_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_counter_query_pred.cpp" />
    <ClCompile Include="d3d11\d3d11_deferred_map.cpp" />
    <ClCompile Include="d3d11\d3d11_deferred_updatesubresource.cpp" />
    <ClCompile Include="d3d11\d3d11_discard_view.cpp" />
    <ClCompile Include="d3d11\d3d11_discard_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_divergent_shader.cpp" />
    <ClCompile Include="d3d11\d3d11_draw_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_empty_capture.cpp" />
    <ClCompile Include="d3d11\d3d11_empty_compute_dispatch.cpp" />
    <ClCompile Include="d3d11\d3d11_empty_drawcall.cpp" />
    <ClCompile Include="d3d11\d3d11_empty_viewports.cpp" />
    <ClCompile Include="d3d11\d3d11_feature_level_9.cpp" />
    <ClCompile Include="d3d11\d3d11_groupshared.cpp" />
    <ClCompile Include="d3d11\d3d11_helpers.cpp" />
    <ClCompile Include="d3d11\d3d11_large_buffer.cpp" />
    <ClCompile Include="d3d11\d3d11_leak_check.cpp" />
    <ClCompile Include="d3d11\d3d11_many_rtvs.cpp" />
    <ClCompile Include="d3d11\d3d11_map_overrun.cpp" />
    <ClCompile Include="d3d11\d3d11_mesh_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_midframe_create.cpp" />
    <ClCompile Include="d3d11\d3d11_mip_gen_rt.cpp" />
    <ClCompile Include="d3d11\d3d11_mip_rtv.cpp" />
    <ClCompile Include="d3d11\d3d11_overdraw_stress.cpp" />
    <ClCompile Include="d3d11\d3d11_parameter_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_pixel_history_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_primitiveid.cpp" />
    <ClCompile Include="d3d11\d3d11_shader_debug_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_overlay_test.cpp" />
    <ClCompile Include="d3d11\d3d11_primitive_restart.cpp" />
    <ClCompile Include="d3d11\d3d11_refcount_check.cpp" />
    <ClCompile Include="d3d11\d3d11_resource_lifetimes.cpp" />
    <ClCompile Include="d3d11\d3d11_saturate.cpp" />
    <ClCompile Include="d3d11\d3d11_shader_editing.cpp" />
    <ClCompile Include="d3d11\d3d11_shader_isa.cpp" />
    <ClCompile Include="d3d11\d3d11_shader_linkage_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_simple_dispatch.cpp" />
    <ClCompile Include="d3d11\d3d11_simple_triangle.cpp" />
    <ClCompile Include="d3d11\d3d11_stream_out.cpp" />
    <ClCompile Include="d3d11\d3d11_stripped_shaders.cpp" />
    <ClCompile Include="d3d11\d3d11_structured_buffer_misaligned_dirty.cpp" />
    <ClCompile Include="d3d11\d3d11_structured_buffer_nested.cpp" />
    <ClCompile Include="d3d11\d3d11_structured_buffer_read.cpp" />
    <ClCompile Include="d3d11\d3d11_swapchain_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_template.cpp" />
    <ClCompile Include="d3d11\d3d11_test.cpp" />
    <ClCompile Include="d3d11\d3d11_texture_3d.cpp" />
    <ClCompile Include="d3d11\d3d11_texture_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_untyped_backbuffer_descriptor.cpp" />
    <ClCompile Include="d3d11\d3d11_vertex_attr_zoo.cpp" />
    <ClCompile Include="d3d11\d3d11_video_textures.cpp" />
    <ClCompile Include="d3d11\d3d11_workgroup_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_amd_shader_extensions.cpp" />
    <ClCompile Include="d3d12\d3d12_buffer_truncation.cpp" />
    <ClCompile Include="d3d12\d3d12_cbuffer_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_compute_only.cpp" />
    <ClCompile Include="d3d12\d3d12_descriptor_indexing.cpp" />
    <ClCompile Include="d3d12\d3d12_discard_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_draw_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_empty_capture.cpp" />
    <ClCompile Include="d3d12\d3d12_execute_indirect.cpp" />
    <ClCompile Include="d3d12\d3d12_existing_heap.cpp" />
    <ClCompile Include="d3d12\d3d12_groupshared.cpp" />
    <ClCompile Include="d3d12\d3d12_helpers.cpp" />
    <ClCompile Include="d3d12\d3d12_mesh_shader.cpp" />
    <ClCompile Include="d3d12\d3d12_multi_wait_before_signal.cpp" />
    <ClCompile Include="d3d12\d3d12_large_buffer.cpp" />
    <ClCompile Include="d3d12\d3d12_leak_check.cpp" />
    <ClCompile Include="d3d12\d3d12_list_alloc_tests.cpp" />
    <ClCompile Include="d3d12\d3d12_list_types.cpp" />
    <ClCompile Include="d3d12\d3d12_map_placed_alias.cpp" />
    <ClCompile Include="d3d12\d3d12_mesh_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_overlay_test.cpp" />
    <ClCompile Include="d3d12\d3d12_parameter_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_pixel_history.cpp" />
    <ClCompile Include="d3d12\d3d12_primitiveid.cpp" />
    <ClCompile Include="d3d12\d3d12_reflection_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_rendertarget_binds.cpp" />
    <ClCompile Include="d3d12\d3d12_render_pass.cpp" />
    <ClCompile Include="d3d12\d3d12_resource_lifetimes.cpp" />
    <ClCompile Include="d3d12\d3d12_resource_mapping_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_rtas_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_shader_debugdata_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_shader_debug_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_shader_editing.cpp" />
    <ClCompile Include="d3d12\d3d12_shader_isa.cpp" />
    <ClCompile Include="d3d12\d3d12_shader_linkage_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_sharing.cpp" />
    <ClCompile Include="d3d12\d3d12_simple_dispatch.cpp" />
    <ClCompile Include="d3d12\d3d12_simple_triangle.cpp" />
    <ClCompile Include="d3d12\d3d12_subgroup_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_swapchain_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_template.cpp" />
    <ClCompile Include="d3d12\d3d12_test.cpp" />
    <ClCompile Include="d3d12\d3d12_texture_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_untyped_backbuffer_descriptor.cpp" />
    <ClCompile Include="d3d12\d3d12_vertex_attr_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_vertex_uav.cpp" />
    <ClCompile Include="d3d12\d3d12_video_textures.cpp" />
    <ClCompile Include="d3d12\d3d12_vrs.cpp" />
    <ClCompile Include="d3d12\d3d12_workgroup_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_write_subresource.cpp" />
    <ClCompile Include="dx\d3d_helpers.cpp" />
    <ClCompile Include="3rdparty\glad\glad.c" />
    <ClCompile Include="3rdparty\glad\glad_egl.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="3rdparty\glad\glad_glx.c">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="3rdparty\glad\glad_wgl.c" />
    <ClCompile Include="gl\gl_buffer_resizing.cpp" />
    <ClCompile Include="gl\gl_buffer_spam.cpp" />
    <ClCompile Include="gl\gl_buffer_truncation.cpp" />
    <ClCompile Include="gl\gl_buffer_updates.cpp" />
    <ClCompile Include="gl\gl_callstacks.cpp" />
    <ClCompile Include="gl\gl_cbuffer_zoo.cpp" />
    <ClCompile Include="gl\gl_depthstencil_fbo.cpp" />
    <ClCompile Include="gl\gl_depth_bounds.cpp" />
    <ClCompile Include="gl\gl_discard_zoo.cpp" />
    <ClCompile Include="gl\gl_draw_zoo.cpp" />
    <ClCompile Include="gl\gl_dx_interop.cpp" />
    <ClCompile Include="gl\gl_empty_capture.cpp" />
    <ClCompile Include="gl\gl_entry_points.cpp" />
    <ClCompile Include="gl\gl_large_bcn_arrays.cpp" />
    <ClCompile Include="gl\gl_large_buffer.cpp" />
    <ClCompile Include="gl\gl_leak_check.cpp" />
    <ClCompile Include="gl\gl_map_overrun.cpp" />
    <ClCompile Include="gl\gl_marker_test.cpp" />
    <ClCompile Include="gl\gl_mesh_zoo.cpp" />
    <ClCompile Include="gl\gl_midframe_context_create.cpp" />
    <ClCompile Include="gl\gl_mip_gen_rt.cpp" />
    <ClCompile Include="gl\gl_multithread_rendering.cpp" />
    <ClCompile Include="gl\gl_multi_window.cpp" />
    <ClCompile Include="gl\gl_overlay_test.cpp" />
    <ClCompile Include="gl\gl_parameter_zoo.cpp" />
    <ClCompile Include="gl\gl_per_type_tex_units.cpp" />
    <ClCompile Include="gl\gl_queries_in_use.cpp" />
    <ClCompile Include="gl\gl_renderbuffer_zoo.cpp" />
    <ClCompile Include="gl\gl_resource_lifetimes.cpp" />
    <ClCompile Include="gl\gl_runtime_bind_prog_to_pipe.cpp" />
    <ClCompile Include="gl\gl_separable_geometry_shader.cpp" />
    <ClCompile Include="gl\gl_shader_editing.cpp" />
    <ClCompile Include="gl\gl_shader_isa.cpp" />
    <ClCompile Include="gl\gl_simple_triangle.cpp" />
    <ClCompile Include="gl\gl_spirv_shader.cpp" />
    <ClCompile Include="gl\gl_state_trashing.cpp" />
    <ClCompile Include="gl\gl_structured_buffer_nested.cpp" />
    <ClCompile Include="gl\gl_template.cpp" />
    <ClCompile Include="gl\gl_test.cpp" />
    <ClCompile Include="gl\gl_test_linux.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="gl\gl_test_android.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="gl\gl_test_win32.cpp" />
    <ClCompile Include="gl\gl_texture_zoo.cpp" />
    <ClCompile Include="gl\gl_unshared_context.cpp" />
    <ClCompile Include="gl\gl_unsized_ms_fbo_attachment.cpp" />
    <ClCompile Include="gl\gl_vao_0.cpp" />
    <ClCompile Include="gl\gl_vertex_attr_zoo.cpp" />
    <ClCompile Include="gl\gl_pixel_history.cpp" />
    <ClCompile Include="linux\linux_platform.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="linux\linux_window.cpp">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="3rdparty\lz4\lz4.c" />
    <ClCompile Include="main.cpp" />
    <ClCompile Include="test_common.cpp" />
    <ClCompile Include="texture_zoo.cpp" />
    <ClCompile Include="vk\vk_blend.cpp" />
    <ClCompile Include="vk\vk_descriptor_buffer.cpp" />
    <ClCompile Include="vk\vk_compute_only.cpp" />
    <ClCompile Include="vk\vk_counters.cpp" />
    <ClCompile Include="vk\vk_custom_border_color.cpp" />
    <ClCompile Include="vk\vk_dedicated_allocation.cpp" />
    <ClCompile Include="vk\vk_descriptor_buffer_analyse.cpp" />
    <ClCompile Include="vk\vk_descriptor_reuse.cpp" />
    <ClCompile Include="vk\vk_descriptor_variable_count.cpp" />
    <ClCompile Include="vk\vk_discard_zoo.cpp" />
    <ClCompile Include="vk\vk_dynamic_rendering.cpp" />
    <ClCompile Include="vk\vk_empty_capture.cpp" />
    <ClCompile Include="vk\vk_extended_dyn_state.cpp" />
    <ClCompile Include="vk\vk_graphics_pipeline.cpp" />
    <ClCompile Include="vk\vk_groupshared.cpp" />
    <ClCompile Include="vk\vk_khr_buffer_address.cpp" />
    <ClCompile Include="vk\vk_large_buffer.cpp" />
    <ClCompile Include="vk\vk_large_descriptor_sets.cpp" />
    <ClCompile Include="vk\vk_leak_check.cpp" />
    <ClCompile Include="vk\vk_load_store_none.cpp" />
    <ClCompile Include="vk\vk_mem_bench.cpp" />
    <ClCompile Include="vk\vk_mesh_shader.cpp" />
    <ClCompile Include="vk\vk_mesh_zoo.cpp" />
    <ClCompile Include="vk\vk_multi_entry.cpp" />
    <ClCompile Include="vk\vk_multi_present.cpp" />
    <ClCompile Include="vk\vk_multi_view.cpp" />
    <ClCompile Include="vk\vk_parameter_zoo.cpp" />
    <ClCompile Include="vk\vk_imageless_framebuffer.cpp" />
    <ClCompile Include="vk\vk_image_layouts.cpp" />
    <ClCompile Include="vk\vk_int8_ibuffer.cpp" />
    <ClCompile Include="vk\vk_line_raster.cpp" />
    <ClCompile Include="vk\vk_misaligned_dirty.cpp" />
    <ClCompile Include="vk\vk_multi_thread_windows.cpp" />
    <ClCompile Include="vk\vk_postponed.cpp" />
    <ClCompile Include="vk\vk_query_pool.cpp" />
    <ClCompile Include="vk\vk_ray_query.cpp" />
    <ClCompile Include="vk\vk_read_before_overwrite.cpp" />
    <ClCompile Include="vk\vk_robustness2.cpp" />
    <ClCompile Include="vk\vk_separate_depth_stencil_layouts.cpp" />
    <ClCompile Include="vk\vk_shader_debug_zoo.cpp" />
    <ClCompile Include="vk\vk_shader_editing.cpp" />
    <ClCompile Include="vk\vk_shader_isa.cpp" />
    <ClCompile Include="vk\vk_shader_printf.cpp" />
    <ClCompile Include="vk\vk_spec_constants.cpp" />
    <ClCompile Include="vk\vk_spirv_13_shaders.cpp" />
    <ClCompile Include="vk\vk_subgroup_zoo.cpp" />
    <ClCompile Include="vk\vk_sync2.cpp" />
    <ClCompile Include="vk\vk_template.cpp" />
    <ClCompile Include="vk\vk_texture_zoo.cpp" />
    <ClCompile Include="vk\vk_triangle_fan.cpp" />
    <ClCompile Include="vk\vk_buffer_truncation.cpp" />
    <ClCompile Include="vk\vk_validation_use.cpp" />
    <ClCompile Include="vk\vk_vertex_attr_zoo.cpp" />
    <ClCompile Include="vk\vk_ext_buffer_address.cpp" />
    <ClCompile Include="vk\vk_cbuffer_zoo.cpp" />
    <ClCompile Include="vk\vk_descriptor_index.cpp" />
    <ClCompile Include="vk\vk_discard_rects.cpp" />
    <ClCompile Include="vk\vk_draw_zoo.cpp" />
    <ClCompile Include="vk\vk_helpers.cpp" />
    <ClCompile Include="vk\vk_indirect.cpp" />
    <ClCompile Include="vk\vk_resource_lifetimes.cpp" />
    <ClCompile Include="vk\vk_structured_buffer_nested.cpp" />
    <ClCompile Include="vk\vk_overlay_test.cpp" />
    <ClCompile Include="vk\vk_pixel_history.cpp" />
    <ClCompile Include="vk\vk_sample_locations.cpp" />
    <ClCompile Include="vk\vk_adv_cbuffer_zoo.cpp" />
    <ClCompile Include="vk\vk_secondary_cmdbuf.cpp" />
    <ClCompile Include="vk\vk_video_textures.cpp" />
    <ClCompile Include="vk\vk_vs_max_desc_set.cpp" />
    <ClCompile Include="vk\vk_simple_triangle.cpp" />
    <ClCompile Include="vk\vk_test.cpp" />
    <ClCompile Include="3rdparty\volk\volk.c" />
    <ClCompile Include="vk\vk_workgroup_zoo.cpp" />
    <ClCompile Include="win32\win32_platform.cpp" />
    <ClCompile Include="win32\win32_window.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="3rdparty\ags\ags_shader_intrinsics_dx11.hlsl.h" />
    <ClInclude Include="3rdparty\ags\ags_shader_intrinsics_dx12.hlsl.h" />
    <ClInclude Include="3rdparty\ags\amd_ags.h" />
    <ClInclude Include="3rdparty\fmt\core.h" />
    <ClInclude Include="3rdparty\fmt\format-inl.h" />
    <ClInclude Include="3rdparty\fmt\format.h" />
    <ClInclude Include="3rdparty\glad\khrplatform.h" />
    <ClInclude Include="3rdparty\md5\md5.h" />
    <ClInclude Include="3rdparty\VulkanMemoryAllocator\vk_mem_alloc.h" />
    <ClInclude Include="android\android_platform.h">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="android\android_window.h">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="d3d11\d3d11_helpers.h" />
    <ClInclude Include="d3d11\d3d11_test.h" />
    <ClInclude Include="d3d12\d3d12_helpers.h" />
    <ClInclude Include="d3d12\d3d12_test.h" />
    <ClInclude Include="dx\d3d_helpers.h" />
    <ClInclude Include="dx\official\d3d11.h" />
    <ClInclude Include="dx\official\d3d11sdklayers.h" />
    <ClInclude Include="dx\official\d3d11_1.h" />
    <ClInclude Include="dx\official\d3d11_2.h" />
    <ClInclude Include="dx\official\d3d11_3.h" />
    <ClInclude Include="dx\official\d3d11_4.h" />
    <ClInclude Include="dx\official\d3d12.h" />
    <ClInclude Include="dx\official\d3d12sdklayers.h" />
    <ClInclude Include="dx\official\d3d8.h" />
    <ClInclude Include="dx\official\d3d8caps.h" />
    <ClInclude Include="dx\official\d3d8types.h" />
    <ClInclude Include="dx\official\d3d9.h" />
    <ClInclude Include="dx\official\d3d9caps.h" />
    <ClInclude Include="dx\official\d3d9types.h" />
    <ClInclude Include="dx\official\d3dcommon.h" />
    <ClInclude Include="dx\official\d3dcompiler.h" />
    <ClInclude Include="dx\official\dxgi.h" />
    <ClInclude Include="dx\official\dxgi1_2.h" />
    <ClInclude Include="dx\official\dxgi1_3.h" />
    <ClInclude Include="dx\official\dxgi1_4.h" />
    <ClInclude Include="dx\official\dxgi1_5.h" />
    <ClInclude Include="dx\official\dxgi1_6.h" />
    <ClInclude Include="dx\official\dxgiformat.h" />
    <ClInclude Include="dx\official\dxgitype.h" />
    <ClInclude Include="dx\official\winapifamily.h" />
    <ClInclude Include="3rdparty\glad\glad.h" />
    <ClInclude Include="3rdparty\glad\glad_egl.h" />
    <ClInclude Include="3rdparty\glad\glad_glx.h" />
    <ClInclude Include="3rdparty\glad\glad_wgl.h" />
    <ClInclude Include="gl\gl_test.h" />
    <ClInclude Include="linux\linux_platform.h">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="linux\linux_window.h">
      <ExcludedFromBuild>true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="3rdparty\lz4\lz4.h" />
    <ClInclude Include="3rdparty\nuklear\nuklear.h" />
    <ClInclude Include="3rdparty\nuklear\nuklear_gdi.h" />
    <ClInclude Include="3rdparty\nuklear\nuklear_xlib.h" />
    <ClInclude Include="3rdparty\shaderc\shaderc.h" />
    <ClInclude Include="renderdoc_app.h" />
    <ClInclude Include="test_common.h" />
    <ClInclude Include="vk\vk_headers.h" />
    <ClInclude Include="vk\vk_helpers.h" />
    <ClInclude Include="vk\vk_test.h" />
    <ClInclude Include="3rdparty\volk\volk.h" />
    <ClInclude Include="vk\official\vulkan\vk_platform.h" />
    <ClInclude Include="vk\official\vulkan\vulkan.h" />
    <ClInclude Include="vk\official\vulkan\vulkan_core.h" />
    <ClInclude Include="vk\official\vulkan\vulkan_win32.h" />
    <ClInclude Include="vk\official\vulkan\vulkan_xcb.h" />
    <ClInclude Include="vk\official\vulkan\vulkan_xlib.h" />
    <ClInclude Include="win32\win32_platform.h" />
    <ClInclude Include="win32\win32_window.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <!-- try to find the latest dxcompiler.dll, as the redist folder isn't reliably  -->
  <UsingTask TaskName="GetDXCDLL" TaskFactory="CodeTaskFactory" AssemblyFile="$(MSBuildToolsPath)\Microsoft.Build.Tasks.Core.dll">
    <ParameterGroup>
      <DXCPath ParameterType="System.String" Required="False" Output="True" />
      <DebugOut ParameterType="System.String" Required="False" Output="True" />
      <BitnessFolder ParameterType="System.String" Required="True" Output="False" />
      <SDKPath ParameterType="System.String" Required="True" Output="False" />
    </ParameterGroup>
    <Task>
      <Code Type="Class" Language="cs">
using System;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;

namespace DXCEnumerateAndCheck {
  public class GetDXCDLL : Microsoft.Build.Utilities.Task {
    public override bool Execute() {
      if(BitnessFolder == "Win32") BitnessFolder = "x86";

      string[] sdkPathsCheck = SDKPath.Split(';');

      foreach(string sdkPathCheck in sdkPathsCheck)
      {
        try
        {
          string[] dirs = System.IO.Directory.GetDirectories(sdkPathCheck, "10.*");
          // sort and reverse so we try from latest to oldest
          System.Array.Sort(dirs);
          System.Array.Reverse(dirs);

          // look for the latest dxc.exe available that works
          foreach(string path in dirs)
          {
            string dxcCheck = System.IO.Path.Combine(path, BitnessFolder, "dxcompiler.dll");
            if(System.IO.File.Exists(dxcCheck))
            {
              DXCPath = System.IO.Path.Combine(path, BitnessFolder);
              return true;
            }
          }
        }
        catch(Exception ex)
        {
          // try next SDK path
        }
      }

      // always succeed even if we don't find dxc
      return true;
    }

    [Microsoft.Build.Framework.Required] public string BitnessFolder { get; set; }
    [Microsoft.Build.Framework.Required] public string SDKPath { get; set; }
    [Microsoft.Build.Framework.Output] public string DXCPath { get; set; }
    [Microsoft.Build.Framework.Output] public string DebugOut { get; set; }
  }
}
      </Code>
    </Task>
  </UsingTask>
  <PropertyGroup>
    <DXCDLLSourceLocation>$(WindowsSdkDir_10)\Redist\D3D\$(BitnessFolder)</DXCDLLSourceLocation>
  </PropertyGroup>
  <Target Name="CopyFiles" AfterTargets="Build">
    <GetDXCDLL BitnessFolder="$(Platform)" SDKPath="$(Registry:HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Microsoft SDKs\Windows\v10.0@InstallationFolder)bin;$(Registry:HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Microsoft SDKs\Windows\v10.0@InstallationFolder)bin" ContinueOnError="WarnAndContinue">
      <Output TaskParameter="DXCPath" PropertyName="DXCDLLFolder" />
      <Output TaskParameter="DebugOut" PropertyName="DebugOutDXC" />
    </GetDXCDLL>
    <Message Condition="$(DXCDLLFolder.Length) &gt; 0" Importance="high" Text="Using DXC/D3DCompiler dlls from $(DXCDLLFolder)." />
    <Message Condition="$(HaveShaderc)=='1'" Importance="high" Text="Built with linked shaderc." />
    <Message Condition="$(HaveShaderc)!='1'" Importance="high" Text="Not built with linked shaderc." />
    <PropertyGroup Condition="$(DXCDLLFolder.Length) &gt; 0">
      <DXCDLLSourceLocation>$(DXCDLLFolder)</DXCDLLSourceLocation>
    </PropertyGroup>
    <Copy SourceFiles="$(DXCDLLSourceLocation)\d3dcompiler_47.dll" DestinationFolder="$(SolutionDir)" Condition="$([System.IO.File]::GetLastWriteTime('$(DXCDLLSourceLocation)\d3dcompiler_47.dll').Ticks) &gt; $([System.IO.File]::GetLastWriteTime('$(SolutionDir)\d3dcompiler_47.dll').Ticks)" />
    <Copy SourceFiles="$(DXCDLLSourceLocation)\dxcompiler.dll" DestinationFolder="$(SolutionDir)" Condition="$([System.IO.File]::GetLastWriteTime('$(DXCDLLSourceLocation)\dxcompiler.dll').Ticks) &gt; $([System.IO.File]::GetLastWriteTime('$(SolutionDir)\dxcompiler.dll').Ticks)" />
    <Copy SourceFiles="$(DXCDLLSourceLocation)\dxil.dll" DestinationFolder="$(SolutionDir)" Condition="$([System.IO.File]::GetLastWriteTime('$(DXCDLLSourceLocation)\dxil.dll').Ticks) &gt; $([System.IO.File]::GetLastWriteTime('$(SolutionDir)\dxil.dll').Ticks)" />
  </Target>
</Project>