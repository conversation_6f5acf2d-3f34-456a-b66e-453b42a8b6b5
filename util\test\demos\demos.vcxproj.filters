﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="d3d11\d3d11_simple_triangle.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_simple_triangle.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_unsized_ms_fbo_attachment.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_overdraw_stress.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_1_many_uavs.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_mip_rtv.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_many_rtvs.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_empty_compute_dispatch.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_array_interpolator.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_empty_drawcall.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_structured_buffer_read.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_empty_viewports.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_simple_dispatch.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_runtime_bind_prog_to_pipe.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_depthstencil_fbo.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_discard_view.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_deferred_updatesubresource.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_stripped_shaders.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_divergent_shader.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_midframe_create.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_byte_address_buffers.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_mip_gen_rt.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_vao_0.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_texture_3d.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_buffer_updates.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_dx_interop.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_saturate.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_map_overrun.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_map_overrun.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_binding_hazards.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_primitive_restart.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_large_bcn_arrays.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_counter_query_pred.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_simple_triangle.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_draw_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_draw_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_cbuffer_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_cbuffer_zoo.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_cbuffer_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_refcount_check.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_spirv_shader.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_multi_window.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_helpers.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_test.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_structured_buffer_misaligned_dirty.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="win32\win32_platform.cpp">
      <Filter>Windows</Filter>
    </ClCompile>
    <ClCompile Include="win32\win32_window.cpp">
      <Filter>Windows</Filter>
    </ClCompile>
    <ClCompile Include="linux\linux_platform.cpp">
      <Filter>Linux</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_helpers.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_test.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_test.cpp">
      <Filter>OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_test_linux.cpp">
      <Filter>OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_test_win32.cpp">
      <Filter>OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_midframe_context_create.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_vs_max_desc_set.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_overlay_test.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_secondary_cmdbuf.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_indirect.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="linux\linux_window.cpp">
      <Filter>Linux</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_helpers.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_test.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_simple_triangle.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="dx\d3d_helpers.cpp">
      <Filter>D3D</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_cbuffer_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_overlay_test.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_overlay_test.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_overlay_test.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp" />
    <ClCompile Include="test_common.cpp" />
    <ClCompile Include="3rdparty\lz4\lz4.c">
      <Filter>3rdparty\lz4</Filter>
    </ClCompile>
    <ClCompile Include="3rdparty\glad\glad.c">
      <Filter>3rdparty\glad</Filter>
    </ClCompile>
    <ClCompile Include="3rdparty\glad\glad_egl.c">
      <Filter>3rdparty\glad</Filter>
    </ClCompile>
    <ClCompile Include="3rdparty\glad\glad_glx.c">
      <Filter>3rdparty\glad</Filter>
    </ClCompile>
    <ClCompile Include="3rdparty\glad\glad_wgl.c">
      <Filter>3rdparty\glad</Filter>
    </ClCompile>
    <ClCompile Include="3rdparty\volk\volk.c">
      <Filter>3rdparty\volk</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_video_textures.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_video_textures.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_video_textures.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_mip_gen_rt.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_separable_geometry_shader.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_sample_locations.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_discard_rects.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_adv_cbuffer_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_descriptor_index.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_structured_buffer_nested.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_structured_buffer_nested.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_structured_buffer_nested.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_entry_points.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_resource_lifetimes.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_resource_lifetimes.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_resource_lifetimes.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_resource_lifetimes.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_vertex_attr_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_vertex_attr_zoo.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_vertex_attr_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_vertex_attr_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_multi_thread_windows.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_per_type_tex_units.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_shader_debug_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_untyped_backbuffer_descriptor.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_untyped_backbuffer_descriptor.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_image_layouts.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_stream_out.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_int8_ibuffer.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_imageless_framebuffer.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_line_raster.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_spec_constants.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_triangle_fan.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_spirv_13_shaders.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_misaligned_dirty.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_shader_editing.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_shader_editing.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_parameter_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_parameter_zoo.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_buffer_spam.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_write_subresource.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_texture_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_texture_zoo.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="texture_zoo.cpp" />
    <ClCompile Include="d3d12\d3d12_texture_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_texture_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_ext_buffer_address.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_khr_buffer_address.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_separate_depth_stencil_layouts.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_shader_editing.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_shader_editing.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_callstacks.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_deferred_map.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_unshared_context.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_mesh_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_mesh_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_mesh_zoo.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_mesh_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_sharing.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_execute_indirect.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_parameter_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_shader_isa.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_shader_isa.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_shader_isa.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_shader_isa.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_shader_debug_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_render_pass.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_resource_mapping_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_primitiveid.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_primitiveid.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_marker_test.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_empty_capture.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_empty_capture.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_empty_capture.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_empty_capture.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_simple_dispatch.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_rendertarget_binds.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_pixel_history_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_large_buffer.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_large_buffer.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_large_buffer.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_large_buffer.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_multithread_rendering.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_shader_debug_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="3rdparty\fmt\format.cc">
      <Filter>3rdparty\fmt</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_query_pool.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_shader_linkage_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_shader_linkage_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_draw_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_draw_zoo.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_custom_border_color.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_robustness2.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_buffer_truncation.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_buffer_truncation.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_buffer_truncation.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_buffer_truncation.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_reflection_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_pixel_history.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_extended_dyn_state.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_discard_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_discard_zoo.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_discard_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_discard_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_state_trashing.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_validation_use.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_existing_heap.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_descriptor_reuse.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_descriptor_variable_count.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_swapchain_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_swapchain_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_feature_level_9.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_queries_in_use.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_large_descriptor_sets.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_list_types.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_list_alloc_tests.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_amd_shader_extensions.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_parameter_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_buffer_resizing.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_leak_check.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_leak_check.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_leak_check.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_leak_check.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_dedicated_allocation.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_sync2.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_renderbuffer_zoo.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_vrs.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_amd_shader_extensions.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="3rdparty\md5\md5.c">
      <Filter>3rdparty\md5</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_descriptor_indexing.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_load_store_none.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_shader_printf.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_multi_entry.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_multi_present.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_dynamic_rendering.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_postponed.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_depth_bounds.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_compute_only.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_compute_only.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_read_before_overwrite.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_graphics_pipeline.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_vertex_uav.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_pixel_history.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_map_placed_alias.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_mem_bench.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="android\android_platform.cpp">
      <Filter>Android</Filter>
    </ClCompile>
    <ClCompile Include="android\android_window.cpp">
      <Filter>Android</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_test_android.cpp">
      <Filter>OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="gl\gl_template.cpp">
      <Filter>OpenGL\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_template.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_template.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_template.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_blend.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_counters.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_pixel_history.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_multi_wait_before_signal.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_multi_view.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_mesh_shader.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_rtas_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_ray_query.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_subgroup_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_groupshared.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_mesh_shader.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_groupshared.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_groupshared.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_descriptor_buffer.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_shader_debugdata_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_subgroup_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_workgroup_zoo.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d12\d3d12_workgroup_zoo.cpp">
      <Filter>D3D12\demos</Filter>
    </ClCompile>
    <ClCompile Include="d3d11\d3d11_workgroup_zoo.cpp">
      <Filter>D3D11\demos</Filter>
    </ClCompile>
    <ClCompile Include="vk\vk_descriptor_buffer_analyse.cpp">
      <Filter>Vulkan\demos</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="D3D11">
      <UniqueIdentifier>{2d925e41-46b9-4fc8-bed8-38f94377d4ce}</UniqueIdentifier>
    </Filter>
    <Filter Include="D3D11\demos">
      <UniqueIdentifier>{ab751575-8361-4303-9660-a511267f4a6e}</UniqueIdentifier>
    </Filter>
    <Filter Include="OpenGL">
      <UniqueIdentifier>{067b0d96-2253-4f97-b4e9-9a398e73698f}</UniqueIdentifier>
    </Filter>
    <Filter Include="OpenGL\demos">
      <UniqueIdentifier>{1da9ebf5-0432-4265-b118-9ffa5857a317}</UniqueIdentifier>
    </Filter>
    <Filter Include="Vulkan">
      <UniqueIdentifier>{edb31c02-2b14-4e5f-8332-bcf87d98e80d}</UniqueIdentifier>
    </Filter>
    <Filter Include="Vulkan\demos">
      <UniqueIdentifier>{e5a064fd-f7f5-41f7-af8f-27d766122f53}</UniqueIdentifier>
    </Filter>
    <Filter Include="Windows">
      <UniqueIdentifier>{970b67b1-1dfa-4c31-89d5-87333c1cf866}</UniqueIdentifier>
    </Filter>
    <Filter Include="Linux">
      <UniqueIdentifier>{9c5ec0e8-32a9-45b7-8cbc-e6507dfff362}</UniqueIdentifier>
    </Filter>
    <Filter Include="D3D12">
      <UniqueIdentifier>{1ce198b8-2024-4567-b8e2-086dd27725e4}</UniqueIdentifier>
    </Filter>
    <Filter Include="D3D12\demos">
      <UniqueIdentifier>{18e43376-43e9-4a0c-b1a9-031b399e9ede}</UniqueIdentifier>
    </Filter>
    <Filter Include="D3D">
      <UniqueIdentifier>{200ba06c-394d-4759-84f8-b499013b4167}</UniqueIdentifier>
    </Filter>
    <Filter Include="D3D\official">
      <UniqueIdentifier>{91707a1b-5bdd-455f-874f-0dd6ec31aefb}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty">
      <UniqueIdentifier>{2c14abdb-f558-4bc3-8f4a-186690f1beb4}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\lz4">
      <UniqueIdentifier>{45c27b46-1f27-407a-9cf3-8f30dd23156c}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\nuklear">
      <UniqueIdentifier>{06e64450-43b4-42d5-bbf3-290c721ae555}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\shaderc">
      <UniqueIdentifier>{e641fa6b-a639-49af-9bc3-387b87bdb066}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\glad">
      <UniqueIdentifier>{526cfb18-bd0f-41b7-ac73-30a2fc90f449}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\volk">
      <UniqueIdentifier>{831fcf8f-1d85-4684-a16e-273475a869b2}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\vma">
      <UniqueIdentifier>{56a97009-9e25-4860-bcf1-76d915260697}</UniqueIdentifier>
    </Filter>
    <Filter Include="Vulkan\official">
      <UniqueIdentifier>{c46e8d15-ce7e-42d4-ae59-1a960527567c}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\fmt">
      <UniqueIdentifier>{47ec0443-2084-47f0-be07-2a633a0c22e4}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\ags">
      <UniqueIdentifier>{329344bd-312a-4cd6-b618-aadeb4eb13cb}</UniqueIdentifier>
    </Filter>
    <Filter Include="3rdparty\md5">
      <UniqueIdentifier>{25327220-a428-4ea2-8894-300df4a61a33}</UniqueIdentifier>
    </Filter>
    <Filter Include="Android">
      <UniqueIdentifier>{cd8bc334-25e7-40a0-99fb-fff5ccf33687}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="d3d11\d3d11_helpers.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="d3d11\d3d11_test.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="win32\win32_platform.h">
      <Filter>Windows</Filter>
    </ClInclude>
    <ClInclude Include="win32\win32_window.h">
      <Filter>Windows</Filter>
    </ClInclude>
    <ClInclude Include="linux\linux_platform.h">
      <Filter>Linux</Filter>
    </ClInclude>
    <ClInclude Include="vk\vk_helpers.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="vk\vk_test.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="gl\gl_test.h">
      <Filter>OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="linux\linux_window.h">
      <Filter>Linux</Filter>
    </ClInclude>
    <ClInclude Include="d3d12\d3d12_helpers.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="d3d12\d3d12_test.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d8.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d8caps.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d8types.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d9.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d9caps.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d9types.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d11.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d11_1.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d11_2.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d11_3.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d11_4.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d11sdklayers.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d12.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3d12sdklayers.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3dcommon.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\d3dcompiler.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\dxgi.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\dxgi1_2.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\dxgi1_3.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\dxgi1_4.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\dxgi1_5.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\dxgi1_6.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\dxgiformat.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\dxgitype.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\official\winapifamily.h">
      <Filter>D3D\official</Filter>
    </ClInclude>
    <ClInclude Include="dx\d3d_helpers.h">
      <Filter>D3D</Filter>
    </ClInclude>
    <ClInclude Include="test_common.h" />
    <ClInclude Include="vk\vk_headers.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\lz4\lz4.h">
      <Filter>3rdparty\lz4</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\nuklear\nuklear.h">
      <Filter>3rdparty\nuklear</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\nuklear\nuklear_gdi.h">
      <Filter>3rdparty\nuklear</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\nuklear\nuklear_xlib.h">
      <Filter>3rdparty\nuklear</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\shaderc\shaderc.h">
      <Filter>3rdparty\shaderc</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\glad\khrplatform.h">
      <Filter>3rdparty\glad</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\glad\glad.h">
      <Filter>3rdparty\glad</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\glad\glad_egl.h">
      <Filter>3rdparty\glad</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\glad\glad_glx.h">
      <Filter>3rdparty\glad</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\glad\glad_wgl.h">
      <Filter>3rdparty\glad</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\volk\volk.h">
      <Filter>3rdparty\volk</Filter>
    </ClInclude>
    <ClInclude Include="vk\official\vulkan\vk_platform.h">
      <Filter>Vulkan\official</Filter>
    </ClInclude>
    <ClInclude Include="vk\official\vulkan\vulkan.h">
      <Filter>Vulkan\official</Filter>
    </ClInclude>
    <ClInclude Include="vk\official\vulkan\vulkan_core.h">
      <Filter>Vulkan\official</Filter>
    </ClInclude>
    <ClInclude Include="vk\official\vulkan\vulkan_win32.h">
      <Filter>Vulkan\official</Filter>
    </ClInclude>
    <ClInclude Include="vk\official\vulkan\vulkan_xcb.h">
      <Filter>Vulkan\official</Filter>
    </ClInclude>
    <ClInclude Include="vk\official\vulkan\vulkan_xlib.h">
      <Filter>Vulkan\official</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\VulkanMemoryAllocator\vk_mem_alloc.h">
      <Filter>3rdparty\vma</Filter>
    </ClInclude>
    <ClInclude Include="renderdoc_app.h" />
    <ClInclude Include="3rdparty\fmt\core.h">
      <Filter>3rdparty\fmt</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\fmt\format.h">
      <Filter>3rdparty\fmt</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\fmt\format-inl.h">
      <Filter>3rdparty\fmt</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\ags\ags_shader_intrinsics_dx11.hlsl.h">
      <Filter>3rdparty\ags</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\ags\ags_shader_intrinsics_dx12.hlsl.h">
      <Filter>3rdparty\ags</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\ags\amd_ags.h">
      <Filter>3rdparty\ags</Filter>
    </ClInclude>
    <ClInclude Include="3rdparty\md5\md5.h">
      <Filter>3rdparty\md5</Filter>
    </ClInclude>
    <ClInclude Include="android\android_platform.h">
      <Filter>Android</Filter>
    </ClInclude>
    <ClInclude Include="android\android_window.h">
      <Filter>Android</Filter>
    </ClInclude>
  </ItemGroup>
</Project>